export const text = {
  eventType: "Event Type",
  eventTypePlaceholder: "Select Event Type",
  eventName: "Event Name",
  hostName: "Host Name",
  optional: "Optional",
  topic: "Topic",
  location: "Location",
  timeZone: "Time Zone",
  eventMode: "Event Mode",
  lobbyMode: "Lobby Mode",
  startDate: "Start Date",
  followMode: "Follow Mode",
  participantMode: "Participant Mode",
  startTime: "Start Time",
  duration: "Duration",
  recurring: " Recurring Meeting",
  recurrence: "Recurrence",
  repeat: "Repeat Every",
  endDate: "End Date",
  by: "By",
  after: "After",
  occurrences: "Occurrences",
  invite: "Invite Participants",
  schedule: "Schedule",
  planMeeting: "Plan a Meeting",
  editMeeting: "Edit a Meeting",
  hello: "Hello",
  whatWould: "What Would You Like To Do?",
  starta: " Start a",
  planA: "Plan a",
  meeting: "Meeting",
  meetings: "Meetings",
  delete: "You want to delete this meeting?",
  meetingname: "Meeting Name",
  meetingPlaceholder: "Enter Meeting Name",
  startMeeting: "Start a Meeting",
  start: "Start",
  invitePeople: "Invite More People",
  shareOthers: "Share the meeting link to invite others",
  shareInvitation: "Share meeting invitation",
  copyInvitationNotification: "Invitation copied",
  copyInvitation: "Copy Meeting Invite",
  linkCopy: "Link copied to clipboard",
  subject: "Subject",
  chapter: "Chapter",
  subTopic: "Sub-Topic",
  recordingLink:"Recording Links",
  clickBelow:"Kindly click below to copy recording link.",
  subTextConferencing:"Enjoy high quality audio/video meetings on Secure, Simple and Reliable platform powered by Daakia.",
  subTextTranslation:"Whether text, document, HTML or media files, translate with high accuracy through our AI powered translation engine.",
  joinMeeting: "Join a Meeting",
  joinMeetingUrl: "Meeting URL",
  joinMeetingPlaceholder: "Enter Meeting URL",
};
